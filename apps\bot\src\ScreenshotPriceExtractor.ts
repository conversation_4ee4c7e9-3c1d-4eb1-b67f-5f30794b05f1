import { Page } from 'playwright'
import Tesseract from 'tesseract.js'
import sharp from 'sharp'
import * as fs from 'fs'
import * as path from 'path'

export interface ChartArea {
	x: number
	y: number
	width: number
	height: number
}

export interface PriceExtractionConfig {
	chartArea?: ChartArea
	priceArea?: ChartArea
	ocrOptions?: any // Using any for now due to Tesseract types
	debugMode?: boolean
	screenshotPath?: string
}

export class ScreenshotPriceExtractor {
	private page: Page
	private config: PriceExtractionConfig
	private worker: Tesseract.Worker | null = null
	private isInitialized = false

	constructor(page: Page, config: PriceExtractionConfig = {}) {
		this.page = page
		this.config = {
			// Default chart area - these coordinates may need adjustment based on Pocket Option's layout
			chartArea: { x: 300, y: 100, width: 800, height: 600 },
			// Default price area - typically in the top-right of the chart
			priceArea: { x: 900, y: 120, width: 200, height: 50 },
			debugMode: false,
			screenshotPath: './screenshots',
			...config
		}
	}

	async initialize(): Promise<void> {
		if (this.isInitialized) return

		try {
			// Initialize Tesseract worker
			this.worker = await Tesseract.createWorker('eng', 1, {
				logger: this.config.debugMode ? m => console.log(m) : undefined
			})

			// Configure OCR for better number recognition
			await this.worker.setParameters({
				tessedit_char_whitelist: '0123456789.,',
				tessedit_pageseg_mode: Tesseract.PSM.SINGLE_LINE,
				preserve_interword_spaces: '0'
			})

			// Ensure screenshot directory exists
			if (this.config.screenshotPath && !fs.existsSync(this.config.screenshotPath)) {
				fs.mkdirSync(this.config.screenshotPath, { recursive: true })
			}

			this.isInitialized = true
			console.log('ScreenshotPriceExtractor initialized successfully')
		} catch (error) {
			console.error('Failed to initialize ScreenshotPriceExtractor:', error)
			throw error
		}
	}

	async extractPriceFromScreenshot(): Promise<number> {
		if (!this.isInitialized) {
			await this.initialize()
		}

		try {
			// Take screenshot of the entire page
			const fullScreenshot = await this.page.screenshot({ type: 'png' })

			// Extract price area from the screenshot
			const priceAreaImage = await this.extractPriceArea(fullScreenshot)

			// Preprocess the image for better OCR accuracy
			const processedImage = await this.preprocessImage(priceAreaImage)

			// Save debug images if enabled
			if (this.config.debugMode && this.config.screenshotPath) {
				const timestamp = Date.now()
				await fs.promises.writeFile(path.join(this.config.screenshotPath, `full_${timestamp}.png`), fullScreenshot)
				await fs.promises.writeFile(
					path.join(this.config.screenshotPath, `price_area_${timestamp}.png`),
					priceAreaImage
				)
				await fs.promises.writeFile(path.join(this.config.screenshotPath, `processed_${timestamp}.png`), processedImage)
			}

			// Perform OCR on the processed image
			const ocrResult = await this.performOCR(processedImage)

			// Extract and validate price from OCR result
			const price = this.extractPriceFromText(ocrResult.data.text)

			if (this.config.debugMode) {
				console.log('OCR Result:', ocrResult.data.text)
				console.log('Extracted Price:', price)
			}

			return price
		} catch (error) {
			console.error('Error extracting price from screenshot:', error)
			return 0
		}
	}

	private async extractPriceArea(fullScreenshot: Buffer): Promise<Buffer> {
		const priceArea = this.config.priceArea!

		return await sharp(fullScreenshot)
			.extract({
				left: priceArea.x,
				top: priceArea.y,
				width: priceArea.width,
				height: priceArea.height
			})
			.png()
			.toBuffer()
	}

	private async preprocessImage(imageBuffer: Buffer): Promise<Buffer> {
		return await sharp(imageBuffer)
			// Resize for better OCR accuracy
			.resize({ width: 400, height: 100, fit: 'fill' })
			// Convert to grayscale
			.grayscale()
			// Increase brightness and saturation for better contrast
			.modulate({ brightness: 1.2, saturation: 1.5 })
			// Apply threshold to make text clearer
			.threshold(128)
			.png()
			.toBuffer()
	}

	private async performOCR(imageBuffer: Buffer): Promise<Tesseract.RecognizeResult> {
		if (!this.worker) {
			throw new Error('OCR worker not initialized')
		}

		return await this.worker.recognize(imageBuffer)
	}

	private extractPriceFromText(text: string): number {
		// Clean the text and extract numeric values
		const cleanText = text.replace(/\s+/g, '').replace(/[^\d.,]/g, '')

		// Look for price patterns (e.g., 1.23456, 123.45, etc.)
		const pricePatterns = [
			/(\d+\.\d{4,6})/, // Forex prices like 1.23456
			/(\d+\.\d{2,3})/, // Stock prices like 123.45
			/(\d+\.\d+)/, // Any decimal number
			/(\d+)/ // Whole numbers as fallback
		]

		for (const pattern of pricePatterns) {
			const match = cleanText.match(pattern)
			if (match) {
				const price = parseFloat(match[1])
				// Validate price range (adjust based on your trading assets)
				if (price > 0.001 && price < 1000000) {
					return price
				}
			}
		}

		return 0
	}

	async findOptimalPriceArea(): Promise<ChartArea | null> {
		try {
			// Take a screenshot and analyze it to find the best price area
			const screenshot = await this.page.screenshot({ type: 'png' })

			// This is a simplified approach - in practice, you might want to:
			// 1. Look for specific UI elements that indicate price display areas
			// 2. Use computer vision to detect text regions
			// 3. Analyze color patterns to find price displays

			// For now, we'll try multiple areas and see which gives the best results
			const testAreas: ChartArea[] = [
				{ x: 900, y: 120, width: 200, height: 50 }, // Top-right
				{ x: 800, y: 100, width: 250, height: 60 }, // Alternative top-right
				{ x: 1000, y: 150, width: 180, height: 40 }, // Far right
				{ x: 850, y: 80, width: 220, height: 70 } // Upper right area
			]

			for (const area of testAreas) {
				const tempConfig = { ...this.config, priceArea: area, debugMode: false }
				const tempExtractor = new ScreenshotPriceExtractor(this.page, tempConfig)
				await tempExtractor.initialize()

				const price = await tempExtractor.extractPriceFromScreenshot()
				if (price > 0) {
					console.log(`Found optimal price area: ${JSON.stringify(area)} with price: ${price}`)
					await tempExtractor.cleanup()
					return area
				}

				await tempExtractor.cleanup()
			}

			return null
		} catch (error) {
			console.error('Error finding optimal price area:', error)
			return null
		}
	}

	updateConfig(newConfig: Partial<PriceExtractionConfig>): void {
		this.config = { ...this.config, ...newConfig }
	}

	async cleanup(): Promise<void> {
		if (this.worker) {
			await this.worker.terminate()
			this.worker = null
		}
		this.isInitialized = false
	}
}
